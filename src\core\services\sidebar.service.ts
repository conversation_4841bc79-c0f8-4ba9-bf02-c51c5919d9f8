import { Injectable, Injector } from '@angular/core';
import { AppMenu } from '@core/models/app-menu';
import { AppMenuItem } from '@core/models/app-menu-item';
import { AppPermissionService } from './permission.checker.service';
import { AppSessionService } from './session.service';

@Injectable()
export class AppSidebarService {
    private permissionService: AppPermissionService;
    private sessionService: AppSessionService;

    constructor(_injector: Injector) {
        this.permissionService = _injector.get(AppPermissionService);
        this.sessionService = _injector.get(AppSessionService);
    }

    getMenu(): AppMenu {
        return new AppMenu('MainMenu', 'MainMenu', [
            new AppMenuItem('fas fa-home', 'Home', '', '/customer/welcome/dashboard'),
             // Módulos Auditoria
             new AppMenuItem('fas fa-folder-open', 'Gestor Documental', 'Pages.Auditory', '', [], [
                new AppMenuItem('fas fa-bell', 'Mis Pendientes', 'Pages.Auditory.Document', '/customer/auditory/document/dashboard'),
                new AppMenuItem('fas fa-file-alt', 'Vigentes', 'Pages.Auditory.Valid', '/customer/auditory/valid-document/dashboard'),
                new AppMenuItem('fas fa-file-excel', 'Obsoletos', 'Pages.Auditory.Obsolete', '/customer/auditory/obsolet/dashboard'),
                new AppMenuItem('fas fa-file', 'Externos', 'Pages.Auditory.DocumentExternal', '/customer/auditory/document-external/dashboard'),
                new AppMenuItem('fas fa-stamp', 'Modelos de Autorizacion', 'Pages.Auditory.Models', '/customer/auditory/models/dashboard'),
                new AppMenuItem('fas fa-diagram-project', 'Monitoreo de Distribución', 'Pages.Auditory.Notification', '/customer/auditory/notification/dashboard'),
                new AppMenuItem('fas fa-user-check', 'Monitoreo de Autorización', 'Pages.Auditory.Notification', '/customer/auditory/notificationstatus/dashboard'),
                new AppMenuItem('fas fa-file-invoice', 'Maestro de Tipo de Documento', 'Pages.Auditory.DocumentType', '/customer/auditory/documenttype/dashboard'),
                new AppMenuItem('fas fa-sitemap', 'Maestro de Áreas', 'Pages.Auditory.Management', '/customer/auditory/management/dashboard'),
                new AppMenuItem('fas fa-project-diagram', 'Maestro de MacroProcesos', 'Pages.Auditory.MacroProcess', '/customer/auditory/macroprocess/dashboard'),
                new AppMenuItem('fas fa-cogs', 'Maestro de Procesos', 'Pages.Auditory.Process', '/customer/auditory/process/dashboard'),
                new AppMenuItem('fas fa-cog', 'Maestro de SubProcesos', 'Pages.Auditory.SubProcess', '/customer/auditory/subprocess/dashboard')

            ]),
            new AppMenuItem('fas fa-satellite-dish', 'Gestión de Auditorías', 'Pages.Auditory.Manager', '', [], [
                new AppMenuItem('fas fa-user-tie', 'Auditoría', 'Pages.Auditory.Manager.Audit', '/customer/auditory/manager/audit/dashboard'),
                new AppMenuItem('fas fa-user-check', 'Normas', 'Pages.Auditory.Manager.Policy', '/customer/auditory/manager/policy/dashboard')
            ]),
            new AppMenuItem('fas fa-user-cog', 'Administración', 'Pages.Management', '', [], [
                new AppMenuItem('fas fa-user-tie', 'Empleados', 'Pages.Management.Employee', '/customer/management/employees/dashboard'),
                
                new AppMenuItem('fas fa-users', 'Personas', 'Pages.Management.Person', '/customer/management/persons/dashboard'),
                
            ]),
            new AppMenuItem('fas fa-shield-alt', 'Seguridad', 'Pages.Management', '', [], [
                new AppMenuItem('fas fa-clipboard-list', 'Inspecciones', 'Pages.Management.Inspection', '/customer/management/inspections/dashboard', [
                    '/customer/management/inspections/import'
                ]),
                new AppMenuItem('fas fa-file-signature', 'ATR', 'Pages.Management.RiskWorkApproval', '/customer/management/risk-work-approvals/dashboard'),
                new AppMenuItem('fas fa-graduation-cap', 'Capacitaciones', 'Pages.Management.Training', '/customer/management/trainings/dashboard'),
                new AppMenuItem('fas fa-bell', 'Pendientes de Capacitación', 'Pages.Management.Training.Pending', '/customer/management/training/pending/dashboard'),

                new AppMenuItem('fas fa-comments', 'Charlas', 'Pages.Management.Meet', '/customer/management/meets/dashboard'),
                new AppMenuItem('fas fa-tv', 'Monitoreo de alarmas', 'Pages.Management.Monitoring', '/customer/management/monitoring/dashboard'),
                new AppMenuItem('fas fa-exclamation-triangle', 'Alarmas', 'Pages.Management.Alarm', '/customer/management/alarms/dashboard'),
                new AppMenuItem('fa-solid fa-id-card', 'Ingreso sin DNI', 'Pages.Management.UndocumentedEntry', '/customer/management/undocumented-entries/dashboard'),
            ]),
            new AppMenuItem('fas fa-money-bill-wave', 'Presupuesto', 'Pages.Management', '', [], [
                new AppMenuItem('fas fa-tasks', 'Tareo', 'Pages.Management.OperationTaskProgram', '/customer/management/operation-task-programs/dashboard'),
                new AppMenuItem('fas fa-headset', 'Llamadas de servicio', 'Pages.Management.ServiceCall', '/customer/management/service-calls/dashboard'),
                new AppMenuItem('fas fa-network-wired', 'Jerarquías', 'Pages.Management.Hierarchy', '/customer/management/hierarchies/dashboard'),
                new AppMenuItem('fas fa-users-cog', 'Proyecto - Personas', 'Pages.Management.OperationTask', '/customer/management/operation-tasks/dashboard'),
            ]),
            new AppMenuItem('fas fa-concierge-bell', 'Atención al Cliente', 'Pages.CustomerService', '', [], [
                new AppMenuItem('fas fa-file-contract', 'Bandeja de Actas', 'Pages.CustomerService.Actas', '/customer/ope3/atencion-cliente/actas/bandeja-actas'),
                new AppMenuItem('fas fa-list-ul', 'Bandeja de Maestro de Tipos de servicio', 'Pages.CustomerService.Services', '/customer/ope3/atencion-cliente/maestro-reservas/bandeja'),
                new AppMenuItem('fas fa-calendar-check', 'Bandeja de Reservas', 'Pages.CustomerService.Reservations', '/customer/ope3/atencion-cliente/reservas/bandeja'),
                new AppMenuItem('fas fa-file-invoice-dollar', 'Bandeja de Facturacion SAP', 'Pages.CustomerService.BillingSAP', '/customer/ope3/atencion-cliente/facturacion-sap'),
                new AppMenuItem('fas fa-file-code', 'Bandeja de tipo documento', 'Pages.CustomerService.TypeDocument', '/customer/ope3/atencion-cliente/tipo-documento'),
                new AppMenuItem('fas fa-file-medical-alt', 'Bandeja de sub tipo documentos', 'Pages.CustomerService.SubTypeDocument', '/customer/ope3/atencion-cliente/sub-tipo-documento'),
                new AppMenuItem('fas fa-box', 'Bandeja de Documentos Pack', 'Pages.CustomerService.DocumentPack', '/customer/ope3/atencion-cliente/documentos-pack/bandeja'),
            ]),
            new AppMenuItem('fas fa-calendar-alt', 'Planificación', 'Pages.Schedule', '', [], [
                new AppMenuItem('fas fa-calendar-day', 'Programa de actividades', 'Pages.Schedule.MaintenanceProgram', '/customer/schedule/maintenance-programs/dashboard'),
                new AppMenuItem('fas fa-tools', 'Equipos/Instalaciones', 'Pages.Schedule.MaintenanceAsset', '/customer/schedule/maintenance-assets/dashboard'),
                new AppMenuItem('fas fa-calendar-plus', 'Planificación preventiva', 'Pages.Schedule.MaintenancePreventive', '/customer/schedule/maintenance-preventives/dashboard'),
                new AppMenuItem('fas fa-wrench', 'Planificación correctiva', 'Pages.Schedule.MaintenanceCorrective', '/customer/schedule/maintenance-correctives/dashboard'),
            ]),
            new AppMenuItem('fas fa-database', 'Maestros', 'Pages.Maintenance', '', [], [
                new AppMenuItem('fas fa-th-large', 'Áreas', 'Pages.Maintenance.PersonArea', '/customer/maintenance/person-areas/dashboard'),
                new AppMenuItem('fas fa-hard-hat', 'Equipos de protección personal básicos', 'Pages.Maintenance.BasicProtectionEquipment', '/customer/maintenance/basic-protection-equipments/dashboard'),
                new AppMenuItem('fas fa-mask', 'Equipos de protección personal específicos', 'Pages.Maintenance.SpecificProtectionEquipment', '/customer/maintenance/specific-protection-equipments/dashboard'),
                new AppMenuItem('fas fa-bell-slash', 'Tipos de alarma', 'Pages.Maintenance.AlarmType', '/customer/maintenance/alarm-types/dashboard'),
                new AppMenuItem('fas fa-chalkboard-teacher', 'Tipos de capacitación', 'Pages.Maintenance.TrainingType', '/customer/maintenance/training-types/dashboard'),
                new AppMenuItem('fas fa-user-graduate', 'Tipos de capacitación dirigida', 'Pages.Maintenance.TrainingMode', '/customer/maintenance/training-modes/dashboard'),
                new AppMenuItem('fas fa-check-square', 'Tipos de inspección', 'Pages.Maintenance.InspectionType', '/customer/maintenance/inspection-types/dashboard'),
                new AppMenuItem('fas fa-briefcase', 'Tipos de trabajo', 'Pages.Maintenance.JobType', '/customer/maintenance/job-types/dashboard'),
                new AppMenuItem('fas fa-comment-dots', 'Motivos de tratamiento de alarmas', 'Pages.Maintenance.AlarmTreatmentReason', '/customer/maintenance/alarm-treatment-reasons/dashboard'),
                new AppMenuItem('fas fa-clipboard-list', 'Cuentas de alarma', 'Pages.Maintenance.AlarmAccount', '/customer/maintenance/alarm-accounts/dashboard'),
                new AppMenuItem('fas fa-sliders-h', 'Parámetros de programa de actividades', 'Pages.Maintenance.MaintenanceProgramParameter', '/customer/maintenance/maintenance-program-parameters/dashboard'),
                new AppMenuItem('fas fa-object-group', 'Familias', 'Pages.Maintenance.MaintenanceFamily', '/customer/maintenance/maintenance-families/dashboard'),
                new AppMenuItem('fas fa-cubes', 'Sistema 1', 'Pages.Maintenance.MaintenanceMacroSystem', '/customer/maintenance/maintenance-macro-systems/dashboard'),
                new AppMenuItem('fas fa-cube', 'Sistema 2', 'Pages.Maintenance.MaintenanceMicroSystem', '/customer/maintenance/maintenance-micro-systems/dashboard'),
                new AppMenuItem('fas fa-microchip', 'Sistema 3', 'Pages.Maintenance.MaintenanceNanoSystem', '/customer/maintenance/maintenance-nano-systems/dashboard'),
                new AppMenuItem('fas fa-warehouse', 'Centros logísticos', 'Pages.Maintenance.MaintenanceCenter', '/customer/maintenance/maintenance-centers/dashboard', [
                    '/customer/maintenance/maintenance-centers/management/'
                ]),
                new AppMenuItem('fas fa-layer-group', 'Problemas de mantenimiento correctivo', 'Pages.Maintenance.MaintenanceActivityProblem', '/customer/maintenance/maintenance-activity-problems/dashboard'),
                new AppMenuItem('fas fa-list-check', 'Tipos de pólizas, seguros y similares', 'Pages.Maintenance.MaintenanceServiceRequestEnsurance', '/customer/maintenance/maintenance-service-request-ensurances/dashboard'),
                new AppMenuItem('fas fa-exclamation-circle', 'Problemas de mantenimiento correctivo', 'Pages.Maintenance.MaintenanceActivityProblem', '/customer/maintenance/maintenance-activity-problems/dashboard'),
            ]),
            new AppMenuItem('fas fa-cogs', 'Configuración', 'Pages.Administration', '', [], [
                new AppMenuItem('fas fa-history', 'Auditoría', 'Pages.Administration.AuditLogs', '/customer/administration/audit-logs/dashboard'),
                new AppMenuItem('fas fa-sliders-h', 'Configuración', 'Pages.Administration.Settings', '/customer/administration/settings/dashboard'),
                new AppMenuItem('fas fa-tools', 'Mantenimiento', 'Pages.Administration.Maintenance', '/customer/administration/maintenance/dashboard'),
                new AppMenuItem('fas fa-file-code', 'Plantillas', 'Pages.Administration.Template', '/customer/administration/templates/dashboard'),
                new AppMenuItem('fas fa-user-tag', 'Roles', 'Pages.Administration.Role', '/customer/administration/roles/dashboard'),
                new AppMenuItem('fas fa-user-friends', 'Usuarios', 'Pages.Administration.User', '/customer/administration/users/dashboard'),
            ]),
            new AppMenuItem('fas fa-truck-moving', 'Tracking', 'Pages.Extras.Tracking', '/customer/ope3/compras/tracking'),

            new AppMenuItem('fas fa-map-marked-alt', 'Planos web', 'Pages.Extras.PlanoWEB', '', [], [
                new AppMenuItem('fa-solid fa-location-dot', 'Portada Lurin', 'Pages.Extras.PlanoWEB.PortadaLurin', '/customer/ope3/plano-web/plano-pl'),
                new AppMenuItem('fa-solid fa-location-dot', 'Villa el Salvador', 'Pages.Extras.PlanoWEB.VillaElSalvador', '/customer/ope3/plano-web/plano-ves'),
                new AppMenuItem('fa-solid fa-location-dot', 'Logiscity', 'Pages.Extras.PlanoWEB.Logiscity', '/customer/ope3/plano-web/plano-logiscity'),
            ]),            

            new AppMenuItem('fas fa-building', 'TAWA', 'Pages.Extras.TAWA', 'https://workin.grupotawa.com/?url=/Portal/Home', [], [], true),
            new AppMenuItem('fas fa-external-link-alt', 'SCA', 'Pages.Extras.SCA', 'https://sca.bsf.pe/', [], [], true),

            new AppMenuItem('fa-solid fa-file-signature', 'Cargos', 'Pages.Cargos', '', [], [
                new AppMenuItem('fa-solid fa-bag-shopping', 'Registro de Cargo', 'Pages.Cargos.LostItem', '/customer/cargos/lost-item/dashboard')
            ]),

        ]);
    }

    checkChildMenuItemPermission(menuItem: AppMenuItem): boolean {
        for (let i = 0; i < menuItem.items.length; i++) {
            let subMenuItem = menuItem.items[i];

            if (subMenuItem.permissionName === '' || subMenuItem.permissionName === null) {
                if (subMenuItem.route) {
                    return true;
                }
            } else if (this.permissionService.isGranted(subMenuItem.permissionName)) {
                return true;
            }

            if (subMenuItem.items && subMenuItem.items.length) {
                let isAnyChildItemActive = this.checkChildMenuItemPermission(subMenuItem);
                if (isAnyChildItemActive) {
                    return true;
                }
            }
        }
        return false;
    }

    showMenuItem(menuItem: AppMenuItem): boolean {
        let hideMenuItem = false;

        if (menuItem.requiresAuthentication && !this.sessionService.user) {
            hideMenuItem = true;
        }

        if (menuItem.permissionName && !this.permissionService.isGranted(menuItem.permissionName)) {
            hideMenuItem = true;
        }

        if (!hideMenuItem && menuItem.items && menuItem.items.length) {
            return this.checkChildMenuItemPermission(menuItem);
        }

        return !hideMenuItem;
    }

    getAllMenuItems(): AppMenuItem[] {
        let menu = this.getMenu();
        let allMenuItems: AppMenuItem[] = [];
        menu.items.forEach((menuItem) => {
            allMenuItems = allMenuItems.concat(this.getAllMenuItemsRecursive(menuItem));
        });

        return allMenuItems;
    }

    private getAllMenuItemsRecursive(menuItem: AppMenuItem): AppMenuItem[] {
        if (!menuItem.items) {
            return [menuItem];
        }

        let menuItems = [menuItem];
        menuItem.items.forEach((subMenu) => {
            menuItems = menuItems.concat(this.getAllMenuItemsRecursive(subMenu));
        });

        return menuItems;
    }
}
